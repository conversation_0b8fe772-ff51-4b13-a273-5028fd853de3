{"version": 3, "names": ["_superPropBase", "require", "_defineProperty", "set", "target", "property", "value", "receiver", "Reflect", "base", "superPropBase", "desc", "Object", "getOwnPropertyDescriptor", "call", "writable", "defineProperty", "_set", "isStrict", "s", "TypeError"], "sources": ["../../src/helpers/set.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport superPropBase from \"./superPropBase.ts\";\nimport defineProperty from \"./defineProperty.ts\";\n\nfunction set(\n  target: object,\n  property: PropertyKey,\n  value: any,\n  receiver?: any,\n): boolean {\n  if (typeof Reflect !== \"undefined\" && Reflect.set) {\n    // @ts-expect-error explicit function reassign\n    set = Reflect.set;\n  } else {\n    // @ts-expect-error explicit function reassign\n    set = function set(target, property, value, receiver) {\n      var base = superPropBase(target, property);\n      var desc;\n\n      if (base) {\n        desc = Object.getOwnPropertyDescriptor(base, property)!;\n        if (desc.set) {\n          desc.set.call(receiver, value);\n          return true;\n          // so getOwnPropertyDescriptor should always be defined\n        } else if (!desc.writable) {\n          // Both getter and non-writable fall into this.\n          return false;\n        }\n      }\n\n      // Without a super that defines the property, spec boils down to\n      // \"define on receiver\" for some reason.\n      desc = Object.getOwnPropertyDescriptor(receiver, property);\n      if (desc) {\n        if (!desc.writable) {\n          // Setter, getter, and non-writable fall into this.\n          return false;\n        }\n\n        desc.value = value;\n        Object.defineProperty(receiver, property, desc);\n      } else {\n        // Avoid setters that may be defined on Sub's prototype, but not on\n        // the instance.\n        defineProperty(receiver, property, value);\n      }\n\n      return true;\n    };\n  }\n\n  return set(target, property, value, receiver);\n}\n\nexport default function _set(\n  target: object,\n  property: PropertyKey,\n  value: any,\n  receiver?: any,\n  isStrict?: boolean,\n) {\n  var s = set(target, property, value, receiver || target);\n  if (!s && isStrict) {\n    throw new TypeError(\"failed to set property\");\n  }\n\n  return value;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AAEA,SAASE,GAAGA,CACVC,MAAc,EACdC,QAAqB,EACrBC,KAAU,EACVC,QAAc,EACL;EACT,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACL,GAAG,EAAE;IAEjDA,GAAG,GAAGK,OAAO,CAACL,GAAG;EACnB,CAAC,MAAM;IAELA,GAAG,GAAG,SAASA,GAAGA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAE;MACpD,IAAIE,IAAI,GAAG,IAAAC,sBAAa,EAACN,MAAM,EAAEC,QAAQ,CAAC;MAC1C,IAAIM,IAAI;MAER,IAAIF,IAAI,EAAE;QACRE,IAAI,GAAGC,MAAM,CAACC,wBAAwB,CAACJ,IAAI,EAAEJ,QAAQ,CAAE;QACvD,IAAIM,IAAI,CAACR,GAAG,EAAE;UACZQ,IAAI,CAACR,GAAG,CAACW,IAAI,CAACP,QAAQ,EAAED,KAAK,CAAC;UAC9B,OAAO,IAAI;QAEb,CAAC,MAAM,IAAI,CAACK,IAAI,CAACI,QAAQ,EAAE;UAEzB,OAAO,KAAK;QACd;MACF;MAIAJ,IAAI,GAAGC,MAAM,CAACC,wBAAwB,CAACN,QAAQ,EAAEF,QAAQ,CAAC;MAC1D,IAAIM,IAAI,EAAE;QACR,IAAI,CAACA,IAAI,CAACI,QAAQ,EAAE;UAElB,OAAO,KAAK;QACd;QAEAJ,IAAI,CAACL,KAAK,GAAGA,KAAK;QAClBM,MAAM,CAACI,cAAc,CAACT,QAAQ,EAAEF,QAAQ,EAAEM,IAAI,CAAC;MACjD,CAAC,MAAM;QAGL,IAAAK,uBAAc,EAACT,QAAQ,EAAEF,QAAQ,EAAEC,KAAK,CAAC;MAC3C;MAEA,OAAO,IAAI;IACb,CAAC;EACH;EAEA,OAAOH,GAAG,CAACC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,CAAC;AAC/C;AAEe,SAASU,IAAIA,CAC1Bb,MAAc,EACdC,QAAqB,EACrBC,KAAU,EACVC,QAAc,EACdW,QAAkB,EAClB;EACA,IAAIC,CAAC,GAAGhB,GAAG,CAACC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,IAAIH,MAAM,CAAC;EACxD,IAAI,CAACe,CAAC,IAAID,QAAQ,EAAE;IAClB,MAAM,IAAIE,SAAS,CAAC,wBAAwB,CAAC;EAC/C;EAEA,OAAOd,KAAK;AACd", "ignoreList": []}