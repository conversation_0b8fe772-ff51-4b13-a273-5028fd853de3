import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Users, 
  Briefcase, 
  DollarSign, 
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Star,
  BarChart3,
  Activity,
  UserCheck,
  FileText
} from 'lucide-react';
import { mockStats, mockTasks, mockWorkers, mockCustomers } from '../../data/mockData';

const AdminDashboard = () => {
  const [stats, setStats] = useState(mockStats);
  const [recentActivity, setRecentActivity] = useState([]);
  const [alerts, setAlerts] = useState([]);

  useEffect(() => {
    // Generate recent activity
    const activity = [
      {
        id: 1,
        type: 'user_signup',
        message: 'New worker registered: <PERSON>',
        timestamp: '2 minutes ago',
        icon: UserCheck,
        color: 'text-green-600'
      },
      {
        id: 2,
        type: 'task_completed',
        message: 'Task completed: Furniture moving in Manhattan',
        timestamp: '15 minutes ago',
        icon: CheckCircle,
        color: 'text-blue-600'
      },
      {
        id: 3,
        type: 'payment_processed',
        message: 'Payment processed: $125 to <PERSON>',
        timestamp: '1 hour ago',
        icon: DollarSign,
        color: 'text-green-600'
      },
      {
        id: 4,
        type: 'dispute_reported',
        message: 'Dispute reported for task #1234',
        timestamp: '2 hours ago',
        icon: AlertCircle,
        color: 'text-red-600'
      }
    ];
    setRecentActivity(activity);

    // Generate alerts
    const systemAlerts = [
      {
        id: 1,
        type: 'warning',
        title: 'High Volume Alert',
        message: '15% increase in task postings today',
        priority: 'medium'
      },
      {
        id: 2,
        type: 'error',
        title: 'Payment Issue',
        message: '3 failed payment transactions need review',
        priority: 'high'
      },
      {
        id: 3,
        type: 'info',
        title: 'System Maintenance',
        message: 'Scheduled maintenance tonight at 2 AM EST',
        priority: 'low'
      }
    ];
    setAlerts(systemAlerts);
  }, []);

  const getAlertColor = (type) => {
    switch (type) {
      case 'error': return 'border-red-200 bg-red-50 text-red-800';
      case 'warning': return 'border-yellow-200 bg-yellow-50 text-yellow-800';
      case 'info': return 'border-blue-200 bg-blue-50 text-blue-800';
      default: return 'border-gray-200 bg-gray-50 text-gray-800';
    }
  };

  const getAlertIcon = (type) => {
    switch (type) {
      case 'error': return <AlertCircle className="w-5 h-5" />;
      case 'warning': return <Clock className="w-5 h-5" />;
      case 'info': return <FileText className="w-5 h-5" />;
      default: return <FileText className="w-5 h-5" />;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Admin Dashboard</h1>
        <p className="text-gray-600">Monitor and manage the Flexlance platform</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-primary-teal">{stats.totalUsers.toLocaleString()}</p>
              <p className="text-sm text-green-600">+12% from last month</p>
            </div>
            <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-primary-teal" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Tasks</p>
              <p className="text-2xl font-bold text-blue-600">{(stats.totalTasks - stats.completedTasks).toLocaleString()}</p>
              <p className="text-sm text-blue-600">+8% from yesterday</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Briefcase className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
              <p className="text-2xl font-bold text-green-600">${stats.monthlyRevenue.toLocaleString()}</p>
              <p className="text-sm text-green-600">+15% from last month</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Satisfaction</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.customerSatisfaction}</p>
              <p className="text-sm text-yellow-600">+0.2 from last month</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {alerts.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">System Alerts</h2>
          <div className="space-y-3">
            {alerts.map((alert) => (
              <div key={alert.id} className={`border rounded-lg p-4 ${getAlertColor(alert.type)}`}>
                <div className="flex items-start space-x-3">
                  {getAlertIcon(alert.type)}
                  <div className="flex-1">
                    <h3 className="font-medium">{alert.title}</h3>
                    <p className="text-sm mt-1">{alert.message}</p>
                  </div>
                  <button className="text-sm font-medium hover:underline">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Quick Actions */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Quick Actions</h2>
            <div className="space-y-3">
              <Link
                to="/admin/users"
                className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Users className="w-5 h-5 text-primary-teal" />
                <span className="font-medium text-gray-700">Manage Users</span>
              </Link>
              
              <Link
                to="/admin/tasks"
                className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Briefcase className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-gray-700">Review Tasks</span>
              </Link>
              
              <Link
                to="/admin/payments"
                className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <DollarSign className="w-5 h-5 text-green-600" />
                <span className="font-medium text-gray-700">Payment Reports</span>
              </Link>
              
              <Link
                to="/admin/analytics"
                className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <BarChart3 className="w-5 h-5 text-purple-600" />
                <span className="font-medium text-gray-700">Analytics</span>
              </Link>
            </div>
          </div>

          {/* Platform Stats */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Platform Overview</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Active Users</span>
                <span className="font-semibold text-gray-800">{stats.activeUsers.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Completed Tasks</span>
                <span className="font-semibold text-gray-800">{stats.completedTasks.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Revenue</span>
                <span className="font-semibold text-gray-800">${stats.totalRevenue.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Avg Task Value</span>
                <span className="font-semibold text-gray-800">${stats.averageTaskValue}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity & Charts */}
        <div className="lg:col-span-2 space-y-6">
          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-800">Recent Activity</h2>
              <Link to="/admin/activity" className="text-primary-teal hover:text-teal-dark text-sm font-medium">
                View All
              </Link>
            </div>
            
            <div className="space-y-4">
              {recentActivity.map((activity) => {
                const IconComponent = activity.icon;
                return (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      activity.color === 'text-green-600' ? 'bg-green-100' :
                      activity.color === 'text-blue-600' ? 'bg-blue-100' :
                      activity.color === 'text-red-600' ? 'bg-red-100' : 'bg-gray-100'
                    }`}>
                      <IconComponent className={`w-4 h-4 ${activity.color}`} />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-800">{activity.message}</p>
                      <p className="text-xs text-gray-500">{activity.timestamp}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Performance Chart Placeholder */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Performance Trends</h2>
            <div className="bg-gray-100 rounded-lg h-64 flex items-center justify-center">
              <div className="text-center">
                <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600">Performance charts would be displayed here</p>
                <p className="text-sm text-gray-500">Revenue, user growth, task completion rates</p>
              </div>
            </div>
          </div>

          {/* Top Performers */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Top Performers This Month</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Top Workers */}
              <div>
                <h3 className="text-lg font-medium text-gray-700 mb-3">Top Workers</h3>
                <div className="space-y-3">
                  {mockWorkers.slice(0, 3).map((worker, index) => (
                    <div key={worker.id} className="flex items-center space-x-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                        index === 0 ? 'bg-yellow-100 text-yellow-800' :
                        index === 1 ? 'bg-gray-100 text-gray-800' :
                        'bg-orange-100 text-orange-800'
                      }`}>
                        {index + 1}
                      </div>
                      <img
                        src={worker.avatar}
                        alt={worker.name}
                        className="w-8 h-8 rounded-full"
                      />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-800">{worker.name}</p>
                        <p className="text-xs text-gray-500">{worker.completedTasks} tasks • ⭐ {worker.rating}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Top Customers */}
              <div>
                <h3 className="text-lg font-medium text-gray-700 mb-3">Top Customers</h3>
                <div className="space-y-3">
                  {mockCustomers.slice(0, 3).map((customer, index) => (
                    <div key={customer.id} className="flex items-center space-x-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                        index === 0 ? 'bg-yellow-100 text-yellow-800' :
                        index === 1 ? 'bg-gray-100 text-gray-800' :
                        'bg-orange-100 text-orange-800'
                      }`}>
                        {index + 1}
                      </div>
                      <img
                        src={customer.avatar}
                        alt={customer.name}
                        className="w-8 h-8 rounded-full"
                      />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-800">{customer.name}</p>
                        <p className="text-xs text-gray-500">{customer.tasksPosted} tasks posted</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
