import React from 'react';

const LoadingSpinner = ({ size = 'md', text = 'Loading...' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const spinnerSize = {
    sm: '1rem',
    md: '2rem',
    lg: '3rem',
    xl: '4rem'
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      backgroundColor: '#F9FAFB'
    }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
        <div
          className="animate-spin"
          style={{
            width: spinnerSize[size],
            height: spinnerSize[size]
          }}>
          <svg
            style={{ width: '100%', height: '100%', color: '#00BFA5' }}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              style={{ opacity: 0.25 }}
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              style={{ opacity: 0.75 }}
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </div>
        {text && (
          <span style={{ color: '#6B7280', fontWeight: '500' }}>{text}</span>
        )}
      </div>

      {/* Flexlance Logo */}
      <div style={{ marginTop: '2rem', textAlign: 'center' }}>
        <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#00BFA5' }}>Flexlance</h2>
        <p style={{ fontSize: '0.875rem', color: '#6B7280', marginTop: '0.25rem' }}>Your Task, Your Price, Your Choice</p>
      </div>


    </div>
  );
};

export default LoadingSpinner;
