import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Eye, 
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  MapPin,
  DollarSign,
  Calendar,
  User,
  MessageSquare
} from 'lucide-react';
import { mockTasks, taskCategories } from '../../data/mockData';

const TaskManagement = () => {
  const [tasks, setTasks] = useState([]);
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [selectedTask, setSelectedTask] = useState(null);
  const [showTaskModal, setShowTaskModal] = useState(false);

  useEffect(() => {
    // Add admin-specific fields to tasks
    const tasksWithAdminData = mockTasks.map(task => ({
      ...task,
      flagged: Math.random() > 0.8,
      disputeReported: Math.random() > 0.9,
      adminNotes: Math.random() > 0.7 ? 'Requires review' : null
    }));
    setTasks(tasksWithAdminData);
    setFilteredTasks(tasksWithAdminData);
  }, []);

  useEffect(() => {
    let filtered = [...tasks];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      if (statusFilter === 'flagged') {
        filtered = filtered.filter(task => task.flagged);
      } else if (statusFilter === 'disputed') {
        filtered = filtered.filter(task => task.disputeReported);
      } else {
        filtered = filtered.filter(task => task.status === statusFilter);
      }
    }

    // Category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(task => task.category === categoryFilter);
    }

    setFilteredTasks(filtered);
  }, [tasks, searchTerm, statusFilter, categoryFilter]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'text-blue-600 bg-blue-100';
      case 'in_progress': return 'text-yellow-600 bg-yellow-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'open': return <Clock className="w-4 h-4" />;
      case 'in_progress': return <AlertTriangle className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'cancelled': return <XCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getCategoryIcon = (categoryId) => {
    const category = taskCategories.find(cat => cat.id === categoryId);
    return category?.icon || '📋';
  };

  const handleTaskAction = async (taskId, action) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setTasks(prevTasks =>
        prevTasks.map(task =>
          task.id === taskId
            ? { 
                ...task, 
                status: action === 'approve' ? 'open' : action === 'cancel' ? 'cancelled' : task.status,
                flagged: action === 'unflag' ? false : task.flagged
              }
            : task
        )
      );
      
      alert(`Task ${action}ed successfully!`);
    } catch (error) {
      alert('Error performing action. Please try again.');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Task Management</h1>
        <p className="text-gray-600">Monitor and manage all tasks on the platform</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Tasks</p>
              <p className="text-2xl font-bold text-gray-800">{tasks.length}</p>
            </div>
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-gray-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Tasks</p>
              <p className="text-2xl font-bold text-blue-600">
                {tasks.filter(t => t.status === 'open' || t.status === 'in_progress').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Flagged Tasks</p>
              <p className="text-2xl font-bold text-red-600">
                {tasks.filter(t => t.flagged).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">
                {tasks.filter(t => t.status === 'completed').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="md:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search tasks by title, description, or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-teal focus:border-transparent"
              />
            </div>
          </div>

          {/* Status Filter */}
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-teal focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="flagged">Flagged</option>
              <option value="disputed">Disputed</option>
            </select>
          </div>

          {/* Category Filter */}
          <div>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-teal focus:border-transparent"
            >
              <option value="all">All Categories</option>
              {taskCategories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="mt-4 text-sm text-gray-600">
          Showing {filteredTasks.length} of {tasks.length} tasks
        </div>
      </div>

      {/* Tasks Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Task
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Budget
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Posted
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTasks.map((task) => (
                <tr key={task.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-start space-x-3">
                      <span className="text-2xl">{getCategoryIcon(task.category)}</span>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-sm font-medium text-gray-900">{task.title}</h3>
                          {task.flagged && (
                            <AlertTriangle className="w-4 h-4 text-red-500" title="Flagged" />
                          )}
                          {task.disputeReported && (
                            <MessageSquare className="w-4 h-4 text-orange-500" title="Dispute Reported" />
                          )}
                        </div>
                        <p className="text-sm text-gray-500 line-clamp-2 mt-1">{task.description}</p>
                        <div className="flex items-center text-xs text-gray-400 mt-1">
                          <MapPin className="w-3 h-3 mr-1" />
                          {task.location}
                        </div>
                        {task.adminNotes && (
                          <div className="text-xs text-orange-600 mt-1 font-medium">
                            Admin Note: {task.adminNotes}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <User className="w-4 h-4 text-gray-400 mr-2" />
                      <div className="text-sm text-gray-900">Customer #{task.postedBy}</div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                      {getStatusIcon(task.status)}
                      <span className="ml-1 capitalize">{task.status.replace('_', ' ')}</span>
                    </span>
                    {task.bids && task.bids.length > 0 && (
                      <div className="text-xs text-gray-500 mt-1">
                        {task.bids.length} bid{task.bids.length !== 1 ? 's' : ''}
                      </div>
                    )}
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-900">
                      <DollarSign className="w-4 h-4 text-gray-400 mr-1" />
                      ${task.budget.min} - ${task.budget.max}
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 text-gray-400 mr-1" />
                      {new Date(task.postedAt).toLocaleDateString()}
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setSelectedTask(task);
                          setShowTaskModal(true);
                        }}
                        className="text-primary-teal hover:text-teal-dark"
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      
                      {task.flagged && (
                        <button
                          onClick={() => handleTaskAction(task.id, 'unflag')}
                          className="text-green-600 hover:text-green-800"
                          title="Remove Flag"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                      )}
                      
                      {task.status === 'open' && (
                        <button
                          onClick={() => handleTaskAction(task.id, 'cancel')}
                          className="text-red-600 hover:text-red-800"
                          title="Cancel Task"
                        >
                          <XCircle className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredTasks.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">No tasks found matching your criteria</div>
          </div>
        )}
      </div>

      {/* Task Detail Modal */}
      {showTaskModal && selectedTask && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-800">Task Details</h3>
                <button
                  onClick={() => setShowTaskModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>
            
            <div className="p-6">
              <div className="flex items-start space-x-4 mb-6">
                <span className="text-3xl">{getCategoryIcon(selectedTask.category)}</span>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h4 className="text-xl font-semibold text-gray-800">{selectedTask.title}</h4>
                    {selectedTask.flagged && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                        <AlertTriangle className="w-3 h-3 mr-1" />
                        Flagged
                      </span>
                    )}
                  </div>
                  <p className="text-gray-600 mb-4">{selectedTask.description}</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">Location:</span>
                      <p className="text-gray-600">{selectedTask.location}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Budget:</span>
                      <p className="text-gray-600">${selectedTask.budget.min} - ${selectedTask.budget.max}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Posted:</span>
                      <p className="text-gray-600">{new Date(selectedTask.postedAt).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Status:</span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ml-2 ${getStatusColor(selectedTask.status)}`}>
                        {getStatusIcon(selectedTask.status)}
                        <span className="ml-1 capitalize">{selectedTask.status.replace('_', ' ')}</span>
                      </span>
                    </div>
                  </div>

                  {selectedTask.requirements && selectedTask.requirements.length > 0 && (
                    <div className="mt-4">
                      <span className="font-medium text-gray-700">Requirements:</span>
                      <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                        {selectedTask.requirements.map((req, index) => (
                          <li key={index}>{req}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {selectedTask.bids && selectedTask.bids.length > 0 && (
                    <div className="mt-6">
                      <h5 className="font-medium text-gray-700 mb-3">Bids ({selectedTask.bids.length})</h5>
                      <div className="space-y-3">
                        {selectedTask.bids.map((bid) => (
                          <div key={bid.id} className="border border-gray-200 rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium text-gray-800">{bid.workerName}</span>
                              <span className="text-lg font-bold text-primary-teal">${bid.amount}</span>
                            </div>
                            <p className="text-sm text-gray-600">{bid.message}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex space-x-3">
                <button className="btn btn-primary">Contact Customer</button>
                <button className="btn btn-outline">View Customer Profile</button>
                {selectedTask.flagged ? (
                  <button
                    onClick={() => handleTaskAction(selectedTask.id, 'unflag')}
                    className="btn btn-outline text-green-600 border-green-600 hover:bg-green-50"
                  >
                    Remove Flag
                  </button>
                ) : (
                  <button className="btn btn-outline text-red-600 border-red-600 hover:bg-red-50">
                    Flag Task
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskManagement;
