{"version": 3, "names": ["_assert<PERSON>lassBrand", "require", "_classPrivateGetter", "privateMap", "receiver", "getter", "assertClassBrand"], "sources": ["../../src/helpers/classPrivateGetter.ts"], "sourcesContent": ["/* @minVersion 7.24.0 */\n\nimport assertClassBrand from \"./assertClassBrand.ts\";\n\nexport default function _classPrivateGetter(\n  privateMap: WeakMap<any, any> | WeakSet<any>,\n  receiver: any,\n  getter: Function,\n) {\n  return getter(assertClassBrand(privateMap, receiver));\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,iBAAA,GAAAC,OAAA;AAEe,SAASC,mBAAmBA,CACzCC,UAA4C,EAC5CC,QAAa,EACbC,MAAgB,EAChB;EACA,OAAOA,MAAM,CAAC,IAAAC,yBAAgB,EAACH,UAAU,EAAEC,QAAQ,CAAC,CAAC;AACvD", "ignoreList": []}