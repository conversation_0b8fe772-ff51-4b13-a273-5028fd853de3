import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Star, 
  Clock, 
  DollarSign, 
  MapPin, 
  MessageCircle, 
  CheckCircle,
  Award,
  Shield,
  TrendingUp,
  Filter,
  SortAsc
} from 'lucide-react';
import { mockTasks, mockWorkers } from '../../data/mockData';

const BidComparison = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const [task, setTask] = useState(null);
  const [bids, setBids] = useState([]);
  const [sortBy, setSortBy] = useState('rating');
  const [filterBy, setFilterBy] = useState('all');
  const [selectedBid, setSelectedBid] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  useEffect(() => {
    // Find the task and its bids
    const foundTask = mockTasks.find(t => t.id === parseInt(taskId));
    if (foundTask) {
      setTask(foundTask);
      
      // Enhance bids with worker details
      const enhancedBids = foundTask.bids?.map(bid => {
        const worker = mockWorkers.find(w => w.id === bid.workerId);
        return {
          ...bid,
          worker: worker || {
            name: bid.workerName,
            rating: bid.workerRating,
            avatar: bid.workerAvatar,
            completedTasks: 0,
            verified: false
          }
        };
      }) || [];
      
      setBids(enhancedBids);
    }
  }, [taskId]);

  const sortBids = (bidsToSort) => {
    return [...bidsToSort].sort((a, b) => {
      switch (sortBy) {
        case 'price_low':
          return a.amount - b.amount;
        case 'price_high':
          return b.amount - a.amount;
        case 'rating':
          return b.worker.rating - a.worker.rating;
        case 'experience':
          return b.worker.completedTasks - a.worker.completedTasks;
        case 'time':
          return new Date(a.submittedAt) - new Date(b.submittedAt);
        default:
          return 0;
      }
    });
  };

  const filterBids = (bidsToFilter) => {
    switch (filterBy) {
      case 'verified':
        return bidsToFilter.filter(bid => bid.worker.verified);
      case 'high_rated':
        return bidsToFilter.filter(bid => bid.worker.rating >= 4.5);
      case 'experienced':
        return bidsToFilter.filter(bid => bid.worker.completedTasks >= 50);
      default:
        return bidsToFilter;
    }
  };

  const getDisplayBids = () => {
    return sortBids(filterBids(bids));
  };

  const handleSelectBid = (bid) => {
    setSelectedBid(bid);
    setShowConfirmModal(true);
  };

  const confirmSelection = async () => {
    try {
      // Simulate API call to accept bid
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert(`Bid accepted! ${selectedBid.worker.name} has been notified and will contact you soon.`);
      navigate('/customer');
    } catch (error) {
      alert('Error accepting bid. Please try again.');
    } finally {
      setShowConfirmModal(false);
      setSelectedBid(null);
    }
  };

  const formatTimeAgo = (dateString) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const getBidRank = (bid, allBids) => {
    const sortedByPrice = [...allBids].sort((a, b) => a.amount - b.amount);
    const priceRank = sortedByPrice.findIndex(b => b.id === bid.id) + 1;
    
    const sortedByRating = [...allBids].sort((a, b) => b.worker.rating - a.worker.rating);
    const ratingRank = sortedByRating.findIndex(b => b.id === bid.id) + 1;
    
    return { priceRank, ratingRank };
  };

  if (!task) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800">Task not found</h1>
          <button onClick={() => navigate('/customer')} className="btn btn-primary mt-4">
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  const displayBids = getDisplayBids();

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate('/customer')}
          className="text-primary-teal hover:text-teal-dark mb-4"
        >
          ← Back to Dashboard
        </button>
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Compare Bids</h1>
        <p className="text-gray-600">Review and select the best worker for your task</p>
      </div>

      {/* Task Summary */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">{task.title}</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <MapPin className="w-4 h-4" />
            <span>{task.location}</span>
          </div>
          <div className="flex items-center space-x-2">
            <DollarSign className="w-4 h-4" />
            <span>Budget: ${task.budget.min} - ${task.budget.max}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4" />
            <span>Est. Time: {task.timeEstimate}</span>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <SortAsc className="w-4 h-4 text-gray-500" />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
              >
                <option value="rating">Sort by Rating</option>
                <option value="price_low">Price: Low to High</option>
                <option value="price_high">Price: High to Low</option>
                <option value="experience">Experience</option>
                <option value="time">Time Submitted</option>
              </select>
            </div>

            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={filterBy}
                onChange={(e) => setFilterBy(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
              >
                <option value="all">All Bids</option>
                <option value="verified">Verified Only</option>
                <option value="high_rated">4.5+ Rating</option>
                <option value="experienced">50+ Tasks</option>
              </select>
            </div>
          </div>

          <div className="text-sm text-gray-600">
            {displayBids.length} of {bids.length} bids shown
          </div>
        </div>
      </div>

      {/* Bids List */}
      <div className="space-y-6">
        {displayBids.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <h3 className="text-lg font-medium text-gray-800 mb-2">No bids yet</h3>
            <p className="text-gray-600">Workers will start bidding on your task soon.</p>
          </div>
        ) : (
          displayBids.map((bid) => {
            const { priceRank, ratingRank } = getBidRank(bid, bids);
            
            return (
              <div key={bid.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <img
                        src={bid.worker.avatar}
                        alt={bid.worker.name}
                        className="w-16 h-16 rounded-full object-cover"
                      />
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="text-lg font-semibold text-gray-800">{bid.worker.name}</h3>
                          {bid.worker.verified && (
                            <Shield className="w-4 h-4 text-primary-teal" title="Verified Worker" />
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <div className="flex items-center space-x-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span>{bid.worker.rating}</span>
                            {ratingRank === 1 && <Award className="w-4 h-4 text-yellow-500" title="Highest Rated" />}
                          </div>
                          <div className="flex items-center space-x-1">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>{bid.worker.completedTasks} tasks completed</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-2xl font-bold text-primary-teal">${bid.amount}</span>
                        {priceRank === 1 && (
                          <TrendingUp className="w-4 h-4 text-green-500" title="Best Price" />
                        )}
                      </div>
                      <div className="text-sm text-gray-600">
                        Est. {bid.estimatedTime}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        Bid submitted {formatTimeAgo(bid.submittedAt)}
                      </div>
                    </div>
                  </div>

                  {/* Bid Message */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <p className="text-gray-700">{bid.message}</p>
                  </div>

                  {/* Rankings */}
                  <div className="flex items-center space-x-6 mb-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-600">Price Rank:</span>
                      <span className={`font-medium ${priceRank === 1 ? 'text-green-600' : 'text-gray-800'}`}>
                        #{priceRank} of {bids.length}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-600">Rating Rank:</span>
                      <span className={`font-medium ${ratingRank === 1 ? 'text-yellow-600' : 'text-gray-800'}`}>
                        #{ratingRank} of {bids.length}
                      </span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between">
                    <div className="flex space-x-3">
                      <button className="btn btn-ghost btn-sm">
                        <MessageCircle className="w-4 h-4 mr-2" />
                        Message
                      </button>
                      <button className="btn btn-ghost btn-sm">
                        View Profile
                      </button>
                    </div>

                    <button
                      onClick={() => handleSelectBid(bid)}
                      className="btn btn-primary"
                    >
                      Accept Bid
                    </button>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Confirmation Modal */}
      {showConfirmModal && selectedBid && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Confirm Bid Selection</h3>
            
            <div className="mb-6">
              <p className="text-gray-600 mb-4">
                You are about to accept the bid from <strong>{selectedBid.worker.name}</strong> for <strong>${selectedBid.amount}</strong>.
              </p>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <img
                    src={selectedBid.worker.avatar}
                    alt={selectedBid.worker.name}
                    className="w-10 h-10 rounded-full"
                  />
                  <div>
                    <div className="font-medium">{selectedBid.worker.name}</div>
                    <div className="text-sm text-gray-600">
                      ⭐ {selectedBid.worker.rating} • {selectedBid.worker.completedTasks} tasks
                    </div>
                  </div>
                </div>
                <p className="text-sm text-gray-700">{selectedBid.message}</p>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmModal(false)}
                className="btn btn-ghost flex-1"
              >
                Cancel
              </button>
              <button
                onClick={confirmSelection}
                className="btn btn-primary flex-1"
              >
                Confirm Selection
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BidComparison;
