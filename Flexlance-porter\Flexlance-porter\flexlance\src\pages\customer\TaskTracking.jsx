import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  MapPin, 
  Clock, 
  Phone, 
  MessageCircle, 
  CheckCircle,
  Navigation,
  Star,
  DollarSign
} from 'lucide-react';
import { mockTasks, mockWorkers } from '../../data/mockData';

const TaskTracking = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const [task, setTask] = useState(null);
  const [worker, setWorker] = useState(null);
  const [trackingData, setTrackingData] = useState({
    status: 'en_route',
    estimatedArrival: '15 minutes',
    currentLocation: 'Broadway & 42nd St',
    progress: 25,
    updates: [
      {
        id: 1,
        time: '2:30 PM',
        message: 'Worker has accepted the task and is on the way',
        type: 'info'
      },
      {
        id: 2,
        time: '2:45 PM',
        message: 'Worker is 5 minutes away from pickup location',
        type: 'update'
      }
    ]
  });

  useEffect(() => {
    // Find the task
    const foundTask = mockTasks.find(t => t.id === parseInt(taskId));
    if (foundTask) {
      setTask(foundTask);
      
      // Find the assigned worker
      const assignedWorker = mockWorkers.find(w => w.id === foundTask.assignedTo);
      setWorker(assignedWorker);
    }

    // Simulate real-time updates
    const interval = setInterval(() => {
      setTrackingData(prev => ({
        ...prev,
        progress: Math.min(prev.progress + 5, 100),
        estimatedArrival: prev.progress < 50 ? '10 minutes' : prev.progress < 80 ? '5 minutes' : 'Arrived'
      }));
    }, 10000);

    return () => clearInterval(interval);
  }, [taskId]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'en_route': return 'text-blue-600 bg-blue-100';
      case 'arrived': return 'text-green-600 bg-green-100';
      case 'in_progress': return 'text-yellow-600 bg-yellow-100';
      case 'completed': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const handleCompleteTask = () => {
    // Navigate to rating/payment page
    navigate(`/customer/complete/${taskId}`);
  };

  if (!task || !worker) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800">Task not found</h1>
          <button onClick={() => navigate('/customer')} className="btn btn-primary mt-4">
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate('/customer')}
          className="text-primary-teal hover:text-teal-dark mb-4"
        >
          ← Back to Dashboard
        </button>
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Track Your Task</h1>
        <p className="text-gray-600">Real-time updates on your task progress</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Tracking Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Task Status */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-800">Task Status</h2>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(trackingData.status)}`}>
                <CheckCircle className="w-4 h-4 mr-1" />
                {trackingData.status.replace('_', ' ').toUpperCase()}
              </span>
            </div>

            <h3 className="text-lg font-medium text-gray-800 mb-2">{task.title}</h3>
            <p className="text-gray-600 mb-4">{task.description}</p>

            {/* Progress Bar */}
            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Progress</span>
                <span>{trackingData.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-primary-teal h-2 rounded-full transition-all duration-500"
                  style={{ width: `${trackingData.progress}%` }}
                ></div>
              </div>
            </div>

            {/* Location & ETA */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <MapPin className="w-5 h-5 text-primary-teal" />
                <div>
                  <div className="text-sm font-medium text-gray-800">Current Location</div>
                  <div className="text-sm text-gray-600">{trackingData.currentLocation}</div>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <Clock className="w-5 h-5 text-primary-teal" />
                <div>
                  <div className="text-sm font-medium text-gray-800">ETA</div>
                  <div className="text-sm text-gray-600">{trackingData.estimatedArrival}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Live Map Placeholder */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Live Tracking</h3>
            <div className="bg-gray-100 rounded-lg h-64 flex items-center justify-center">
              <div className="text-center">
                <Navigation className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600">Interactive map would be displayed here</p>
                <p className="text-sm text-gray-500">Showing worker's real-time location</p>
              </div>
            </div>
          </div>

          {/* Updates Timeline */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Updates</h3>
            <div className="space-y-4">
              {trackingData.updates.map((update) => (
                <div key={update.id} className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary-teal rounded-full mt-2"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <p className="text-gray-800">{update.message}</p>
                      <span className="text-sm text-gray-500">{update.time}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Worker Info */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Your Worker</h3>
            
            <div className="flex items-center space-x-3 mb-4">
              <img
                src={worker.avatar}
                alt={worker.name}
                className="w-12 h-12 rounded-full object-cover"
              />
              <div>
                <h4 className="font-medium text-gray-800">{worker.name}</h4>
                <div className="flex items-center space-x-1 text-sm text-gray-600">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span>{worker.rating}</span>
                  <span>•</span>
                  <span>{worker.completedTasks} tasks</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <button className="w-full btn btn-primary">
                <Phone className="w-4 h-4 mr-2" />
                Call Worker
              </button>
              <button className="w-full btn btn-outline">
                <MessageCircle className="w-4 h-4 mr-2" />
                Send Message
              </button>
            </div>
          </div>

          {/* Task Details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Task Details</h3>
            
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Category:</span>
                <span className="font-medium capitalize">{task.category}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Budget:</span>
                <span className="font-medium">${task.budget.min} - ${task.budget.max}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Estimated Time:</span>
                <span className="font-medium">{task.timeEstimate}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Urgency:</span>
                <span className="font-medium capitalize">{task.urgency}</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
            
            <div className="space-y-3">
              <button className="w-full btn btn-ghost text-left">
                Report an Issue
              </button>
              <button className="w-full btn btn-ghost text-left">
                Modify Task
              </button>
              <button className="w-full btn btn-ghost text-left">
                Cancel Task
              </button>
            </div>
          </div>

          {/* Complete Task Button */}
          {trackingData.progress >= 100 && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-green-800 mb-2">Task Completed!</h3>
              <p className="text-green-700 text-sm mb-4">
                Your worker has marked this task as complete. Please review and rate their work.
              </p>
              <button 
                onClick={handleCompleteTask}
                className="w-full btn btn-primary"
              >
                <Star className="w-4 h-4 mr-2" />
                Rate & Pay
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TaskTracking;
