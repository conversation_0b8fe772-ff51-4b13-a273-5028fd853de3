import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Load user from localStorage on app start
  useEffect(() => {
    const savedUser = localStorage.getItem('flexlance_user');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error('Error parsing saved user:', error);
        localStorage.removeItem('flexlance_user');
      }
    }
    setLoading(false);
  }, []);

  // Save user to localStorage whenever user state changes
  useEffect(() => {
    if (user) {
      localStorage.setItem('flexlance_user', JSON.stringify(user));
    } else {
      localStorage.removeItem('flexlance_user');
    }
  }, [user]);

  const login = async (email, password, role) => {
    try {
      // Simulate API call - In real app, this would be an actual API request
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock user data based on role
      const userData = {
        id: Date.now(),
        email,
        role,
        name: role === 'customer' ? 'John Doe' : 
              role === 'worker' ? 'Jane Smith' : 
              'Admin User',
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(email)}&background=00BFA5&color=fff`,
        verified: role === 'worker' ? true : false,
        rating: role === 'worker' ? 4.8 : null,
        completedTasks: role === 'worker' ? 127 : role === 'customer' ? 23 : null,
        joinedDate: new Date().toISOString(),
        phone: '+1234567890',
        location: 'New York, NY'
      };

      setUser(userData);
      return { success: true, user: userData };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const signup = async (userData) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newUser = {
        id: Date.now(),
        ...userData,
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.name)}&background=00BFA5&color=fff`,
        verified: false,
        rating: userData.role === 'worker' ? 0 : null,
        completedTasks: 0,
        joinedDate: new Date().toISOString()
      };

      setUser(newUser);
      return { success: true, user: newUser };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const logout = () => {
    setUser(null);
  };

  const updateProfile = async (updates) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      return { success: true, user: updatedUser };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const value = {
    user,
    loading,
    login,
    signup,
    logout,
    updateProfile,
    isAuthenticated: !!user,
    isCustomer: user?.role === 'customer',
    isWorker: user?.role === 'worker',
    isAdmin: user?.role === 'admin'
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
