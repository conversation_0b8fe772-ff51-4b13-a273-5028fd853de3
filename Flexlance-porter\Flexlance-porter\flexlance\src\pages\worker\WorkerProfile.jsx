import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { 
  Camera, 
  Star, 
  MapPin, 
  Phone, 
  Mail, 
  Calendar,
  Award,
  Shield,
  Edit,
  Save,
  X,
  Plus,
  DollarSign
} from 'lucide-react';
import { taskCategories } from '../../data/mockData';

const WorkerProfile = () => {
  const { user, updateProfile } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    location: user?.location || '',
    bio: user?.bio || '',
    hourlyRate: user?.hourlyRate || '',
    specialties: user?.specialties || [],
    availability: user?.availability || 'available'
  });
  const [newSpecialty, setNewSpecialty] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const addSpecialty = (categoryId) => {
    if (!profileData.specialties.includes(categoryId)) {
      setProfileData(prev => ({
        ...prev,
        specialties: [...prev.specialties, categoryId]
      }));
    }
  };

  const removeSpecialty = (categoryId) => {
    setProfileData(prev => ({
      ...prev,
      specialties: prev.specialties.filter(id => id !== categoryId)
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const result = await updateProfile(profileData);
      if (result.success) {
        setIsEditing(false);
        alert('Profile updated successfully!');
      } else {
        alert('Error updating profile. Please try again.');
      }
    } catch (error) {
      alert('Error updating profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setProfileData({
      name: user?.name || '',
      email: user?.email || '',
      phone: user?.phone || '',
      location: user?.location || '',
      bio: user?.bio || '',
      hourlyRate: user?.hourlyRate || '',
      specialties: user?.specialties || [],
      availability: user?.availability || 'available'
    });
    setIsEditing(false);
  };

  const getSpecialtyName = (categoryId) => {
    const category = taskCategories.find(cat => cat.id === categoryId);
    return category ? `${category.icon} ${category.name}` : categoryId;
  };

  // Mock reviews data
  const reviews = [
    {
      id: 1,
      customerName: 'Sarah Johnson',
      rating: 5,
      comment: 'Excellent work! Very professional and completed the task perfectly.',
      date: '2024-06-20',
      taskTitle: 'Furniture Assembly'
    },
    {
      id: 2,
      customerName: 'Mike Chen',
      rating: 5,
      comment: 'Great communication and quality work. Highly recommended!',
      date: '2024-06-18',
      taskTitle: 'House Cleaning'
    },
    {
      id: 3,
      customerName: 'Emily Davis',
      rating: 4,
      comment: 'Good work overall, arrived on time and was very careful.',
      date: '2024-06-15',
      taskTitle: 'Moving Assistance'
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-800">My Profile</h1>
          {!isEditing ? (
            <button
              onClick={() => setIsEditing(true)}
              className="btn btn-primary"
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit Profile
            </button>
          ) : (
            <div className="flex space-x-2">
              <button
                onClick={handleCancel}
                className="btn btn-ghost"
              >
                <X className="w-4 h-4 mr-2" />
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={loading}
                className="btn btn-primary"
              >
                <Save className="w-4 h-4 mr-2" />
                {loading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Profile Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Basic Information</h2>
            
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                  {isEditing ? (
                    <input
                      type="text"
                      name="name"
                      value={profileData.name}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-teal focus:border-transparent"
                    />
                  ) : (
                    <p className="text-gray-800">{user?.name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  {isEditing ? (
                    <input
                      type="email"
                      name="email"
                      value={profileData.email}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-teal focus:border-transparent"
                    />
                  ) : (
                    <p className="text-gray-800">{user?.email}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                  {isEditing ? (
                    <input
                      type="tel"
                      name="phone"
                      value={profileData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-teal focus:border-transparent"
                    />
                  ) : (
                    <p className="text-gray-800">{user?.phone || 'Not provided'}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                  {isEditing ? (
                    <input
                      type="text"
                      name="location"
                      value={profileData.location}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-teal focus:border-transparent"
                    />
                  ) : (
                    <p className="text-gray-800">{user?.location}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Hourly Rate</label>
                {isEditing ? (
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="number"
                      name="hourlyRate"
                      value={profileData.hourlyRate}
                      onChange={handleInputChange}
                      className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-teal focus:border-transparent"
                      placeholder="25"
                    />
                  </div>
                ) : (
                  <p className="text-gray-800">${user?.hourlyRate || 'Not set'}/hour</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                {isEditing ? (
                  <textarea
                    name="bio"
                    value={profileData.bio}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-teal focus:border-transparent"
                    placeholder="Tell customers about your experience and skills..."
                  />
                ) : (
                  <p className="text-gray-800">{user?.bio || 'No bio provided'}</p>
                )}
              </div>
            </div>
          </div>

          {/* Specialties */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Specialties</h2>
            
            {isEditing ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {taskCategories.map((category) => (
                    <button
                      key={category.id}
                      type="button"
                      onClick={() => 
                        profileData.specialties.includes(category.id)
                          ? removeSpecialty(category.id)
                          : addSpecialty(category.id)
                      }
                      className={`p-3 border-2 rounded-lg text-center transition-all ${
                        profileData.specialties.includes(category.id)
                          ? 'border-primary-teal bg-teal-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="text-2xl mb-1">{category.icon}</div>
                      <div className={`text-xs font-medium ${
                        profileData.specialties.includes(category.id) ? 'text-primary-teal' : 'text-gray-700'
                      }`}>
                        {category.name}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex flex-wrap gap-2">
                {user?.specialties && user.specialties.length > 0 ? (
                  user.specialties.map((specialty) => (
                    <span
                      key={specialty}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-teal text-white"
                    >
                      {getSpecialtyName(specialty)}
                    </span>
                  ))
                ) : (
                  <p className="text-gray-500">No specialties selected</p>
                )}
              </div>
            )}
          </div>

          {/* Reviews */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Customer Reviews</h2>
            
            <div className="space-y-4">
              {reviews.map((review) => (
                <div key={review.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h4 className="font-medium text-gray-800">{review.customerName}</h4>
                      <p className="text-sm text-gray-500">{review.taskTitle}</p>
                    </div>
                    <div className="flex items-center space-x-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-4 h-4 ${
                            star <= review.rating
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                      <span className="text-sm text-gray-500 ml-2">{review.date}</span>
                    </div>
                  </div>
                  <p className="text-gray-700">{review.comment}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Profile Photo */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="relative inline-block mb-4">
              <img
                src={user?.avatar}
                alt={user?.name}
                className="w-24 h-24 rounded-full object-cover mx-auto"
              />
              {isEditing && (
                <button className="absolute bottom-0 right-0 w-8 h-8 bg-primary-teal text-white rounded-full flex items-center justify-center hover:bg-teal-dark">
                  <Camera className="w-4 h-4" />
                </button>
              )}
            </div>
            <h3 className="text-lg font-semibold text-gray-800">{user?.name}</h3>
            <p className="text-gray-600 capitalize">{user?.role}</p>
            
            {user?.verified && (
              <div className="flex items-center justify-center space-x-1 mt-2 text-primary-teal">
                <Shield className="w-4 h-4" />
                <span className="text-sm font-medium">Verified Worker</span>
              </div>
            )}
          </div>

          {/* Stats */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Performance Stats</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Star className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm text-gray-600">Rating</span>
                </div>
                <span className="font-semibold text-gray-800">{user?.rating || 4.8}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Award className="w-4 h-4 text-green-500" />
                  <span className="text-sm text-gray-600">Tasks Completed</span>
                </div>
                <span className="font-semibold text-gray-800">{user?.completedTasks || 127}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-blue-500" />
                  <span className="text-sm text-gray-600">Member Since</span>
                </div>
                <span className="font-semibold text-gray-800">
                  {user?.joinedDate ? new Date(user.joinedDate).getFullYear() : '2024'}
                </span>
              </div>
            </div>
          </div>

          {/* Availability */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Availability</h3>
            
            {isEditing ? (
              <select
                name="availability"
                value={profileData.availability}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-teal focus:border-transparent"
              >
                <option value="available">Available</option>
                <option value="busy">Busy</option>
                <option value="unavailable">Unavailable</option>
              </select>
            ) : (
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${
                  user?.availability === 'available' ? 'bg-green-500' :
                  user?.availability === 'busy' ? 'bg-yellow-500' : 'bg-red-500'
                }`}></div>
                <span className="capitalize font-medium text-gray-800">
                  {user?.availability || 'Available'}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkerProfile;
