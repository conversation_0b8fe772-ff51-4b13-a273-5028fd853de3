// Mock data for Flexlance application

export const taskCategories = [
  { id: 'delivery', name: 'Delivery', icon: '🚚', description: 'Package and food delivery' },
  { id: 'moving', name: 'Moving & Lifting', icon: '📦', description: 'Furniture moving and heavy lifting' },
  { id: 'cleaning', name: 'Cleaning', icon: '🧹', description: 'House and office cleaning' },
  { id: 'handyman', name: '<PERSON><PERSON>', icon: '🔧', description: 'Minor repairs and maintenance' },
  { id: 'gardening', name: 'Gardening', icon: '🌱', description: 'Lawn care and gardening' },
  { id: 'assembly', name: 'Assembly', icon: '🛠️', description: 'Furniture and equipment assembly' },
  { id: 'painting', name: 'Painting', icon: '🎨', description: 'Interior and exterior painting' },
  { id: 'shopping', name: 'Shopping', icon: '🛒', description: 'Grocery and personal shopping' }
];

export const mockTasks = [
  {
    id: 1,
    title: 'Move furniture to new apartment',
    description: 'Need help moving a couch, dining table, and some boxes from my current apartment to a new one. Both locations have elevators.',
    category: 'moving',
    location: 'Manhattan, NY',
    pickupLocation: '123 Main St, Manhattan, NY 10001',
    dropoffLocation: '456 Oak Ave, Manhattan, NY 10002',
    budget: { min: 80, max: 120 },
    timeEstimate: '3-4 hours',
    urgency: 'medium',
    status: 'open',
    postedBy: 'customer1',
    postedAt: '2024-06-23T10:00:00Z',
    scheduledFor: '2024-06-24T14:00:00Z',
    requirements: ['Strong lifting capability', 'Own transportation preferred'],
    photos: ['https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'],
    bids: [
      {
        id: 'bid1',
        workerId: 'worker1',
        workerName: 'Mike Johnson',
        workerRating: 4.8,
        workerAvatar: 'https://ui-avatars.com/api/?name=Mike+Johnson&background=00BFA5&color=fff',
        amount: 95,
        estimatedTime: '3 hours',
        message: 'I have experience with furniture moving and my own truck. Can start immediately.',
        submittedAt: '2024-06-23T11:30:00Z'
      },
      {
        id: 'bid2',
        workerId: 'worker2',
        workerName: 'Sarah Davis',
        workerRating: 4.9,
        workerAvatar: 'https://ui-avatars.com/api/?name=Sarah+Davis&background=00BFA5&color=fff',
        amount: 110,
        estimatedTime: '4 hours',
        message: 'Professional moving service with insurance coverage. Very careful with furniture.',
        submittedAt: '2024-06-23T12:15:00Z'
      }
    ]
  },
  {
    id: 2,
    title: 'Grocery shopping and delivery',
    description: 'Need someone to shop for groceries from my list and deliver to my home. List includes fresh produce, dairy, and pantry items.',
    category: 'shopping',
    location: 'Brooklyn, NY',
    pickupLocation: 'Whole Foods Market, Brooklyn, NY',
    dropoffLocation: '789 Pine St, Brooklyn, NY 11201',
    budget: { min: 25, max: 40 },
    timeEstimate: '1-2 hours',
    urgency: 'high',
    status: 'open',
    postedBy: 'customer2',
    postedAt: '2024-06-23T09:00:00Z',
    scheduledFor: '2024-06-23T16:00:00Z',
    requirements: ['Reliable transportation', 'Careful with fresh items'],
    shoppingList: ['Organic vegetables', 'Milk', 'Bread', 'Chicken breast', 'Pasta'],
    bids: []
  },
  {
    id: 3,
    title: 'House cleaning service',
    description: 'Deep cleaning needed for 2-bedroom apartment. Kitchen, bathrooms, living areas, and bedrooms. Supplies will be provided.',
    category: 'cleaning',
    location: 'Queens, NY',
    budget: { min: 60, max: 90 },
    timeEstimate: '4-5 hours',
    urgency: 'low',
    status: 'in_progress',
    postedBy: 'customer3',
    assignedTo: 'worker3',
    postedAt: '2024-06-22T14:00:00Z',
    scheduledFor: '2024-06-23T10:00:00Z',
    requirements: ['Experience with deep cleaning', 'Attention to detail'],
    bids: []
  }
];

export const mockWorkers = [
  {
    id: 'worker1',
    name: 'Mike Johnson',
    email: '<EMAIL>',
    avatar: 'https://ui-avatars.com/api/?name=Mike+Johnson&background=00BFA5&color=fff',
    rating: 4.8,
    completedTasks: 127,
    specialties: ['moving', 'handyman', 'assembly'],
    location: 'Manhattan, NY',
    verified: true,
    joinedDate: '2023-03-15',
    bio: 'Experienced mover and handyman with 5+ years in the industry. I take pride in careful handling of your belongings.',
    hourlyRate: 25,
    availability: 'available',
    reviews: [
      {
        id: 'review1',
        customerId: 'customer1',
        customerName: 'John Doe',
        rating: 5,
        comment: 'Excellent service! Very professional and careful with my furniture.',
        date: '2024-06-20'
      }
    ]
  },
  {
    id: 'worker2',
    name: 'Sarah Davis',
    email: '<EMAIL>',
    avatar: 'https://ui-avatars.com/api/?name=Sarah+Davis&background=00BFA5&color=fff',
    rating: 4.9,
    completedTasks: 89,
    specialties: ['cleaning', 'shopping', 'delivery'],
    location: 'Brooklyn, NY',
    verified: true,
    joinedDate: '2023-07-22',
    bio: 'Detail-oriented cleaner and personal shopper. I ensure quality service every time.',
    hourlyRate: 22,
    availability: 'available'
  },
  {
    id: 'worker3',
    name: 'Alex Rodriguez',
    email: '<EMAIL>',
    avatar: 'https://ui-avatars.com/api/?name=Alex+Rodriguez&background=00BFA5&color=fff',
    rating: 4.7,
    completedTasks: 156,
    specialties: ['painting', 'handyman', 'gardening'],
    location: 'Queens, NY',
    verified: true,
    joinedDate: '2022-11-10',
    bio: 'Professional painter and gardener with attention to detail and quality workmanship.',
    hourlyRate: 28,
    availability: 'busy'
  }
];

export const mockCustomers = [
  {
    id: 'customer1',
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: 'https://ui-avatars.com/api/?name=John+Doe&background=1A237E&color=fff',
    location: 'Manhattan, NY',
    joinedDate: '2024-01-15',
    tasksPosted: 12,
    tasksCompleted: 10
  },
  {
    id: 'customer2',
    name: 'Emily Chen',
    email: '<EMAIL>',
    avatar: 'https://ui-avatars.com/api/?name=Emily+Chen&background=1A237E&color=fff',
    location: 'Brooklyn, NY',
    joinedDate: '2024-03-22',
    tasksPosted: 8,
    tasksCompleted: 7
  }
];

export const mockNotifications = [
  {
    id: 1,
    type: 'bid_received',
    title: 'New bid received',
    message: 'Mike Johnson placed a bid on your moving task',
    timestamp: '2024-06-23T11:30:00Z',
    read: false,
    taskId: 1
  },
  {
    id: 2,
    type: 'task_completed',
    title: 'Task completed',
    message: 'Your cleaning task has been marked as completed',
    timestamp: '2024-06-22T16:00:00Z',
    read: true,
    taskId: 3
  },
  {
    id: 3,
    type: 'payment_received',
    title: 'Payment received',
    message: 'You received $85 for completing the delivery task',
    timestamp: '2024-06-22T14:30:00Z',
    read: false
  }
];

export const mockEarnings = {
  today: 125,
  thisWeek: 680,
  thisMonth: 2340,
  total: 12450,
  pending: 95,
  history: [
    { date: '2024-06-23', amount: 125, tasks: 2 },
    { date: '2024-06-22', amount: 85, tasks: 1 },
    { date: '2024-06-21', amount: 150, tasks: 3 },
    { date: '2024-06-20', amount: 95, tasks: 2 },
    { date: '2024-06-19', amount: 110, tasks: 2 }
  ]
};

export const mockStats = {
  totalUsers: 15420,
  activeUsers: 8930,
  totalTasks: 45670,
  completedTasks: 42150,
  totalRevenue: 892340,
  monthlyRevenue: 78450,
  averageTaskValue: 85,
  customerSatisfaction: 4.7
};
