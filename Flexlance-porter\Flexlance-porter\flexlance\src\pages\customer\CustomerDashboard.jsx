import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { 
  Plus, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  DollarSign,
  MapPin,
  Star,
  Eye,
  MessageCircle
} from 'lucide-react';
import { mockTasks, taskCategories } from '../../data/mockData';

const CustomerDashboard = () => {
  const { user } = useAuth();
  const [tasks, setTasks] = useState([]);
  const [stats, setStats] = useState({
    active: 0,
    completed: 0,
    pending: 0,
    totalSpent: 0
  });

  useEffect(() => {
    // Filter tasks for current user and calculate stats
    const userTasks = mockTasks.filter(task => task.postedBy === user?.id || task.postedBy === 'customer1');
    setTasks(userTasks);

    const activeCount = userTasks.filter(task => task.status === 'open' || task.status === 'in_progress').length;
    const completedCount = userTasks.filter(task => task.status === 'completed').length;
    const pendingCount = userTasks.filter(task => task.status === 'pending_payment').length;
    const totalSpent = userTasks.reduce((sum, task) => sum + (task.finalAmount || 0), 0);

    setStats({
      active: activeCount,
      completed: completedCount,
      pending: pendingCount,
      totalSpent
    });
  }, [user]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'text-blue-600 bg-blue-100';
      case 'in_progress': return 'text-yellow-600 bg-yellow-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'pending_payment': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'open': return <Clock className="w-4 h-4" />;
      case 'in_progress': return <AlertCircle className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getCategoryIcon = (categoryId) => {
    const category = taskCategories.find(cat => cat.id === categoryId);
    return category?.icon || '📋';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Welcome Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          Welcome back, {user?.name}!
        </h1>
        <p className="text-gray-600">Manage your tasks and find reliable workers for your needs.</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Tasks</p>
              <p className="text-2xl font-bold text-primary-teal">{stats.active}</p>
            </div>
            <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-primary-teal" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{stats.completed}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Payment</p>
              <p className="text-2xl font-bold text-orange-600">{stats.pending}</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <AlertCircle className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Spent</p>
              <p className="text-2xl font-bold text-primary-navy">${stats.totalSpent}</p>
            </div>
            <div className="w-12 h-12 bg-navy-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-primary-navy" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            to="/customer/post-task"
            className="flex items-center justify-center p-4 border-2 border-dashed border-primary-teal rounded-lg hover:bg-teal-50 transition-colors group"
          >
            <div className="text-center">
              <Plus className="w-8 h-8 text-primary-teal mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <p className="font-medium text-primary-teal">Post New Task</p>
              <p className="text-sm text-gray-500">Get help with your tasks</p>
            </div>
          </Link>

          <Link
            to="/customer/tasks"
            className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
          >
            <div className="text-center">
              <Eye className="w-8 h-8 text-gray-600 mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <p className="font-medium text-gray-700">View All Tasks</p>
              <p className="text-sm text-gray-500">Manage your tasks</p>
            </div>
          </Link>

          <Link
            to="/customer/messages"
            className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
          >
            <div className="text-center">
              <MessageCircle className="w-8 h-8 text-gray-600 mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <p className="font-medium text-gray-700">Messages</p>
              <p className="text-sm text-gray-500">Chat with workers</p>
            </div>
          </Link>
        </div>
      </div>

      {/* Recent Tasks */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-800">Recent Tasks</h2>
            <Link
              to="/customer/tasks"
              className="text-primary-teal hover:text-teal-dark font-medium text-sm"
            >
              View All
            </Link>
          </div>
        </div>

        <div className="divide-y divide-gray-200">
          {tasks.slice(0, 5).map((task) => (
            <div key={task.id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className="text-2xl">{getCategoryIcon(task.category)}</span>
                    <h3 className="font-semibold text-gray-800">{task.title}</h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                      {getStatusIcon(task.status)}
                      <span className="ml-1 capitalize">{task.status.replace('_', ' ')}</span>
                    </span>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">{task.description}</p>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-4 h-4" />
                      <span>{task.location}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <DollarSign className="w-4 h-4" />
                      <span>${task.budget.min} - ${task.budget.max}</span>
                    </div>
                    {task.bids && task.bids.length > 0 && (
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4" />
                        <span>{task.bids.length} bid{task.bids.length !== 1 ? 's' : ''}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex flex-col space-y-2 ml-4">
                  {task.status === 'open' && task.bids && task.bids.length > 0 && (
                    <Link
                      to={`/customer/bids/${task.id}`}
                      className="btn btn-primary btn-sm"
                    >
                      View Bids ({task.bids.length})
                    </Link>
                  )}
                  
                  {task.status === 'in_progress' && (
                    <Link
                      to={`/customer/track/${task.id}`}
                      className="btn btn-secondary btn-sm"
                    >
                      Track Progress
                    </Link>
                  )}
                  
                  <Link
                    to={`/customer/task/${task.id}`}
                    className="btn btn-ghost btn-sm"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {tasks.length === 0 && (
          <div className="p-12 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Plus className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-800 mb-2">No tasks yet</h3>
            <p className="text-gray-600 mb-4">Get started by posting your first task</p>
            <Link to="/customer/post-task" className="btn btn-primary">
              Post Your First Task
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerDashboard;
