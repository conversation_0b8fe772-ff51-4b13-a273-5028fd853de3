import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { User, Briefcase, Shield, Eye, EyeOff } from 'lucide-react';

const LoginPage = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    role: 'customer'
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { login } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const result = await login(formData.email, formData.password, formData.role);
    
    if (!result.success) {
      setError(result.error || 'Login failed. Please try again.');
    }
    
    setLoading(false);
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const roleOptions = [
    { value: 'customer', label: 'Customer', icon: User, description: 'Post tasks and hire workers' },
    { value: 'worker', label: 'Worker', icon: Briefcase, description: 'Find and complete tasks' },
    { value: 'admin', label: 'Admin', icon: Shield, description: 'Manage platform operations' }
  ];

  // Demo credentials for easy testing
  const demoCredentials = {
    customer: { email: '<EMAIL>', password: 'demo123' },
    worker: { email: '<EMAIL>', password: 'demo123' },
    admin: { email: '<EMAIL>', password: 'demo123' }
  };

  const fillDemoCredentials = (role) => {
    setFormData({
      ...formData,
      email: demoCredentials[role].email,
      password: demoCredentials[role].password,
      role
    });
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #00BFA5 0%, #1A237E 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '1rem'
    }}>
      <div style={{ maxWidth: '28rem', width: '100%' }}>
        {/* Logo and Header */}
        <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
          <h1 style={{ fontSize: '2.5rem', fontWeight: 'bold', color: 'white', marginBottom: '0.5rem' }}>Flexlance</h1>
          <p style={{ color: '#B2DFDB' }}>Your Task, Your Price, Your Choice</p>
        </div>

        {/* Login Form */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '1rem',
          boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
          padding: '2rem'
        }}>
          <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1F2937', marginBottom: '1.5rem', textAlign: 'center' }}>Welcome Back</h2>

          {error && (
            <div style={{
              backgroundColor: '#FEF2F2',
              border: '1px solid #FECACA',
              color: '#B91C1C',
              padding: '0.75rem 1rem',
              borderRadius: '0.5rem',
              marginBottom: '1.5rem'
            }}>
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            {/* Role Selection */}
            <div>
              <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.75rem' }}>
                Select Your Role
              </label>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '0.75rem' }}>
                {roleOptions.map((option) => {
                  const IconComponent = option.icon;
                  return (
                    <label
                      key={option.value}
                      style={{
                        position: 'relative',
                        display: 'flex',
                        alignItems: 'center',
                        padding: '1rem',
                        border: `2px solid ${formData.role === option.value ? '#00BFA5' : '#E5E7EB'}`,
                        borderRadius: '0.5rem',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        backgroundColor: formData.role === option.value ? '#F0FDFA' : 'white'
                      }}
                    >
                      <input
                        type="radio"
                        name="role"
                        value={option.value}
                        checked={formData.role === option.value}
                        onChange={handleChange}
                        style={{ position: 'absolute', opacity: 0 }}
                      />
                      <IconComponent style={{
                        width: '1.25rem',
                        height: '1.25rem',
                        marginRight: '0.75rem',
                        color: formData.role === option.value ? '#00BFA5' : '#9CA3AF'
                      }} />
                      <div style={{ flex: 1 }}>
                        <div style={{
                          fontWeight: '500',
                          color: formData.role === option.value ? '#00BFA5' : '#374151'
                        }}>
                          {option.label}
                        </div>
                        <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>{option.description}</div>
                      </div>
                      <button
                        type="button"
                        onClick={() => fillDemoCredentials(option.value)}
                        style={{
                          fontSize: '0.75rem',
                          color: '#00BFA5',
                          fontWeight: '500',
                          background: 'none',
                          border: 'none',
                          cursor: 'pointer'
                        }}
                      >
                        Demo
                      </button>
                    </label>
                  );
                })}
              </div>
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem 1rem',
                  border: '1px solid #D1D5DB',
                  borderRadius: '0.5rem',
                  fontSize: '0.875rem',
                  outline: 'none',
                  transition: 'border-color 0.2s ease'
                }}
                placeholder="Enter your email"
                onFocus={(e) => e.target.style.borderColor = '#00BFA5'}
                onBlur={(e) => e.target.style.borderColor = '#D1D5DB'}
              />
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                Password
              </label>
              <div style={{ position: 'relative' }}>
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem 3rem 0.75rem 1rem',
                    border: '1px solid #D1D5DB',
                    borderRadius: '0.5rem',
                    fontSize: '0.875rem',
                    outline: 'none',
                    transition: 'border-color 0.2s ease'
                  }}
                  placeholder="Enter your password"
                  onFocus={(e) => e.target.style.borderColor = '#00BFA5'}
                  onBlur={(e) => e.target.style.borderColor = '#D1D5DB'}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  style={{
                    position: 'absolute',
                    right: '0.75rem',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: '#9CA3AF',
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer'
                  }}
                >
                  {showPassword ? <EyeOff style={{ width: '1.25rem', height: '1.25rem' }} /> : <Eye style={{ width: '1.25rem', height: '1.25rem' }} />}
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              style={{
                width: '100%',
                backgroundColor: loading ? '#9CA3AF' : '#00BFA5',
                color: 'white',
                padding: '0.75rem 1rem',
                borderRadius: '0.5rem',
                fontWeight: '500',
                border: 'none',
                cursor: loading ? 'not-allowed' : 'pointer',
                transition: 'background-color 0.2s ease',
                opacity: loading ? 0.5 : 1
              }}
              onMouseEnter={(e) => {
                if (!loading) e.target.style.backgroundColor = '#00695C';
              }}
              onMouseLeave={(e) => {
                if (!loading) e.target.style.backgroundColor = '#00BFA5';
              }}
            >
              {loading ? 'Signing In...' : 'Sign In'}
            </button>
          </form>

          {/* Sign Up Link */}
          <div style={{ marginTop: '1.5rem', textAlign: 'center' }}>
            <p style={{ color: '#6B7280' }}>
              Don't have an account?{' '}
              <Link to="/signup" style={{ color: '#00BFA5', fontWeight: '500', textDecoration: 'none' }}>
                Sign up here
              </Link>
            </p>
          </div>

          {/* Demo Info */}
          <div style={{ marginTop: '1.5rem', padding: '1rem', backgroundColor: '#F9FAFB', borderRadius: '0.5rem' }}>
            <p style={{ fontSize: '0.75rem', color: '#6B7280', textAlign: 'center' }}>
              <strong>Demo Mode:</strong> Click "Demo" next to any role to auto-fill credentials
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
